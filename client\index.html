<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Agent - Real-time Voice Conversation</title>
    <link rel="stylesheet" href="src/styles/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="public/favicon.ico">
</head>

<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <h1>🎙️ Audio Agent</h1>
                    <span class="subtitle">Real-time AI Voice Conversation</span>
                </div>
                <div class="connection-status">
                    <div id="status-indicator" class="status-indicator disconnected">
                        <span id="status-text">Disconnected</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                
                <!-- 对话模式选择 - 连接后显示 -->
                <div class="mode-selection" id="modeSelection" style="display: none;">
                    <h3>🎯 选择对话模式</h3>
                    <div class="mode-buttons">
                        <button id="mode-standard" class="mode-btn" data-mode="standard">
                            <span class="mode-icon">🤖</span>
                            <span class="mode-title">标准助手</span>
                            <span class="mode-desc">通用AI助手，回答各种问题</span>
                        </button>
                        <button id="mode-interviewer" class="mode-btn" data-mode="interviewer">
                            <span class="mode-icon">🎯</span>
                            <span class="mode-title">面试官模式</span>
                            <span class="mode-desc">专业面试官，进行技术面试</span>
                        </button>
                    </div>
                </div>
                <div class="conversation-control">
                    <div class="main-control">
                        <button id="conversationBtn" class="conversation-btn" data-state="disconnected">
                            <span class="btn-icon">�️</span>
                            <span class="btn-text">开始对话</span>
                            <div class="btn-status">点击连接服务器并开始对话</div>
                        </button>
                    </div>

                </div>
            </div>

            <!-- Audio Visualization -->
            <div class="audio-section">
                <div class="audio-panel">
                    <h3>🎤 音频输入</h3>
                    <canvas id="visualizer" class="audio-visualizer" width="400" height="100"></canvas>
                    <div id="input-waveform" class="waveform">Audio visualization will appear here</div>
                    <div class="audio-info">
                        <span id="input-volume">音量: 0</span>
                    </div>
                    <div id="volumeMeter" class="volume-meter">
                        <div class="volume-bar"></div>
                    </div>
                </div>

                <div class="audio-panel">
                    <h3>🔊 音频输出</h3>
                    <div id="output-waveform" class="waveform">Audio visualization will appear here</div>
                </div>
            </div>

            <!-- Transcript Status Container -->
            <div id="transcriptStatusContainer" class="transcript-status-container" style="display: none;">
                <div class="status-panel">
                    <div class="status-item">
                        <span class="status-label">🎤 用户输入:</span>
                        <span id="transcriptStatus" class="status-text">等待语音输入...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">🤖 AI状态:</span>
                        <span id="aiStatus" class="status-text">等待AI响应...</span>
                    </div>
                </div>
            </div>

            <!-- Chat Container -->
            <div class="chat-container">
                <div class="chat-header">
                    <h3>💬 对话</h3>
                    <button id="clearBtn" class="clear-btn">清空</button>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message">
                        <div class="system-message">
                            <div class="message-content">
                                <p>👋 欢迎使用 Audio Agent！</p>
                                <p>🤖请选择对话模式，然后点击"连接服务器"开始语音对话。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Error Container -->
        <div id="errorContainer" class="error-container" style="display: none;"></div>
    </div>

    <!-- Modal for Help/Settings -->
    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button id="modal-close" class="close-btn">&times;</button>
            </div>
            <div id="modal-body" class="modal-body">
                <!-- Dynamic content -->
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script type="module" src="src/utils/audio.js"></script>
    <script type="module" src="src/utils/websocket.js"></script>
    <script type="module" src="src/components/ChatMessage.js"></script>
    <script type="module" src="src/app.js"></script>
</body>

</html>